import Decimal from "decimal.js";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import { clearSqliteDb, createSqliteDb, connectDb, clearDb, closeDb } from "../../../tests/utils/db";
import { AccountingValidationService } from "../../accountingValidationService";
import { buildUser, buildReward } from "../../../tests/utils/generateModels";
import { AccountingEventType, LedgerAccounts } from "../../../types/accounting";
import AccountingLedgerStorageService from "../../../external-services/accountingLedgerStorageService";
import { ProviderEnum } from "../../../configs/providersConfig";

describe("AccountingValidationService.validateRewardsDbWithLedger", () => {
  const TODAY = "2025-01-15";
  const FROMDATE = "2025-01-01";

  beforeAll(async () => {
    await connectDb("validateRewardsDbWithLedger");
    await createSqliteDb();
  });

  afterAll(async () => {
    await closeDb();
  });

  beforeEach(async () => {
    // Clear all test data
    await clearSqliteDb();
    await clearDb();
  });

  /* ------------------------------------------------------------------
   * Reward Deposit Settlement Tests
   * ------------------------------------------------------------------ */
  describe("Reward Deposit Settlement", () => {
    it("should validate reward deposit settlement with matching ledger entries", async () => {
      const REWARD_AMOUNT_CENTS = 500_000; // €5,000.00 in cents
      const REWARD_AMOUNT_EUROS = Decimal.div(REWARD_AMOUNT_CENTS, 100).toNumber(); // 5000 euros

      // Create European user
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Create reward with settled deposit
      const reward = await buildReward({
        targetUser: user.id,
        consideration: { amount: REWARD_AMOUNT_CENTS, currency: "EUR" },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-deposit-1",
              status: "Settled",
              submittedAt: new Date(TODAY)
            }
          }
        },
        updatedAt: new Date(TODAY)
      });

      // Create matching ledger entries for deposit settlement
      const description = `${user.id}|${reward.id}|${AccountingEventType.BONUS}`;
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: REWARD_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: undefined,
          document_id: reward.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.BONUS_EXPENSE,
          side: "credit",
          amount: REWARD_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: undefined,
          document_id: reward.id,
          owner_id: user.id
        }
      ]);

      const results = await AccountingValidationService.validateRewardsDbWithLedger(FROMDATE);

      expect(results).toHaveLength(2);

      const depositResult = results.find((r) => r.transactionType === "rewards_deposit");
      expect(depositResult).toEqual(
        expect.objectContaining({
          transactionType: "rewards_deposit",
          isValid: true,
          dbTotalAmount: REWARD_AMOUNT_EUROS,
          ledgerTotalAmount: REWARD_AMOUNT_EUROS,
          difference: 0,
          transactionCount: 1,
          ledgerEntryCount: 2
        })
      );
      expect(depositResult!.discrepancies).toBeUndefined();
    });

    it("should detect discrepancies in reward deposit settlement", async () => {
      const REWARD_AMOUNT_CENTS = 300_000; // €3,000.00 in cents
      const REWARD_AMOUNT_EUROS = Decimal.div(REWARD_AMOUNT_CENTS, 100).toNumber();
      const LEDGER_AMOUNT_EUROS = 2500; // Different amount in ledger

      // Create European user
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Create reward with settled deposit
      const reward = await buildReward({
        targetUser: user.id,
        consideration: { amount: REWARD_AMOUNT_CENTS, currency: "EUR" },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-deposit-1",
              status: "Settled",
              submittedAt: new Date(TODAY)
            }
          }
        },
        updatedAt: new Date(TODAY)
      });

      // Create mismatched ledger entries
      const description = `${user.id}|${reward.id}|${AccountingEventType.BONUS}`;
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: LEDGER_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: undefined,
          document_id: reward.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.BONUS_EXPENSE,
          side: "credit",
          amount: LEDGER_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: undefined,
          document_id: reward.id,
          owner_id: user.id
        }
      ]);

      const results = await AccountingValidationService.validateRewardsDbWithLedger(FROMDATE);

      const depositResult = results.find((r) => r.transactionType === "rewards_deposit");
      expect(depositResult).toBeDefined();
      expect(depositResult!.isValid).toBe(false);
      expect(depositResult!.dbTotalAmount).toBe(REWARD_AMOUNT_EUROS);
      expect(depositResult!.ledgerTotalAmount).toBe(LEDGER_AMOUNT_EUROS);
      expect(depositResult!.difference).toBe(REWARD_AMOUNT_EUROS - LEDGER_AMOUNT_EUROS);
      expect(depositResult!.discrepancies).toHaveLength(1);
      expect(depositResult!.discrepancies![0].transactionId).toBe(reward.id);
      expect(depositResult!.discrepancies![0].difference).toBe(REWARD_AMOUNT_EUROS - LEDGER_AMOUNT_EUROS);
    });

    it("should handle multiple reward deposits correctly", async () => {
      const REWARD1_AMOUNT_CENTS = 2000 * 100; // €2,000.00
      const REWARD2_AMOUNT_CENTS = 3500 * 100; // €3,500.00
      const TOTAL_AMOUNT_EUROS = Decimal.div(REWARD1_AMOUNT_CENTS + REWARD2_AMOUNT_CENTS, 100).toNumber();

      // Create two European users
      const user1 = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      const user2 = await buildUser({
        residencyCountry: "FR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Create two rewards with settled deposits
      const reward1 = await buildReward({
        targetUser: user1.id,
        consideration: { amount: REWARD1_AMOUNT_CENTS, currency: "EUR" },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-deposit-1",
              status: "Settled",
              submittedAt: new Date(TODAY)
            }
          }
        },
        updatedAt: new Date(TODAY)
      });

      const reward2 = await buildReward({
        targetUser: user2.id,
        consideration: { amount: REWARD2_AMOUNT_CENTS, currency: "EUR" },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-deposit-2",
              status: "Settled",
              submittedAt: new Date(TODAY)
            }
          }
        },
        updatedAt: new Date(TODAY)
      });

      // Create matching ledger entries for both rewards
      const reward1Amount = Decimal.div(REWARD1_AMOUNT_CENTS, 100).toNumber();
      const reward2Amount = Decimal.div(REWARD2_AMOUNT_CENTS, 100).toNumber();

      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        // Reward 1 entries
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: reward1Amount,
          description: `${user1.id}|${reward1.id}|${AccountingEventType.BONUS}`,
          article_date: TODAY,
          reference_number: undefined,
          document_id: reward1.id,
          owner_id: user1.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.BONUS_EXPENSE,
          side: "credit",
          amount: reward1Amount,
          description: `${user1.id}|${reward1.id}|${AccountingEventType.BONUS}`,
          article_date: TODAY,
          reference_number: undefined,
          document_id: reward1.id,
          owner_id: user1.id
        },
        // Reward 2 entries
        {
          aa: 2,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: reward2Amount,
          description: `${user2.id}|${reward2.id}|${AccountingEventType.BONUS}`,
          article_date: TODAY,
          reference_number: undefined,
          document_id: reward2.id,
          owner_id: user2.id
        },
        {
          aa: 2,
          account_code: LedgerAccounts.BONUS_EXPENSE,
          side: "credit",
          amount: reward2Amount,
          description: `${user2.id}|${reward2.id}|${AccountingEventType.BONUS}`,
          article_date: TODAY,
          reference_number: undefined,
          document_id: reward2.id,
          owner_id: user2.id
        }
      ]);

      const results = await AccountingValidationService.validateRewardsDbWithLedger(FROMDATE);

      const depositResult = results.find((r) => r.transactionType === "rewards_deposit");
      expect(depositResult).toEqual(
        expect.objectContaining({
          transactionType: "rewards_deposit",
          isValid: true,
          dbTotalAmount: TOTAL_AMOUNT_EUROS,
          ledgerTotalAmount: TOTAL_AMOUNT_EUROS,
          difference: 0,
          transactionCount: 2,
          ledgerEntryCount: 4
        })
      );
      expect(depositResult!.discrepancies).toBeUndefined();
    });
  });

  /* ------------------------------------------------------------------
   * Reward Order Settlement Tests
   * ------------------------------------------------------------------ */
  describe("Reward Order Settlement", () => {
    it("should validate reward order settlement with fees", async () => {
      const REWARD_AMOUNT_CENTS = 400_000; // €4,000.00 in cents
      const FX_FEE_AMOUNT_EUROS = 20; // €20.00 FX fee
      const FX_FEE_AMOUNT_CENTS = Decimal.mul(FX_FEE_AMOUNT_EUROS, 100).toNumber();
      const TOTAL_CLIENT_IMPACT_EUROS = Decimal.div(REWARD_AMOUNT_CENTS + FX_FEE_AMOUNT_CENTS, 100).toNumber();

      // Create European user (domestic client)
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Create reward with settled order
      const reward = await buildReward({
        targetUser: user.id,
        consideration: { amount: REWARD_AMOUNT_CENTS, currency: "EUR" },
        fees: {
          fx: { amount: FX_FEE_AMOUNT_EUROS, currency: "EUR" },
          commission: { amount: 0, currency: "EUR" },
          executionSpread: { amount: 0, currency: "EUR" }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-order-1",
              status: "Matched",
              submittedAt: new Date(TODAY)
            }
          }
        },
        status: "Settled",
        updatedAt: new Date(TODAY)
      });

      // Create matching ledger entries for order settlement
      const description = `${user.id}|${reward.id}|${AccountingEventType.BONUS}`;
      const netAmount = Decimal.div(REWARD_AMOUNT_CENTS, 100).toNumber();

      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        // Client account debits (net settlement + FX fee)
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: netAmount,
          description,
          article_date: TODAY,
          reference_number: "INV-001",
          document_id: reward.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: FX_FEE_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: "INV-001",
          document_id: reward.id,
          owner_id: user.id
        },
        // Omnibus credit (total client impact including fees)
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: TOTAL_CLIENT_IMPACT_EUROS,
          description,
          article_date: TODAY,
          reference_number: "INV-001",
          document_id: reward.id,
          owner_id: user.id
        }
      ]);

      const results = await AccountingValidationService.validateRewardsDbWithLedger(FROMDATE);

      const orderResult = results.find((r) => r.transactionType === "rewards_order");
      expect(orderResult).toEqual(
        expect.objectContaining({
          transactionType: "rewards_order",
          isValid: true,
          dbTotalAmount: TOTAL_CLIENT_IMPACT_EUROS,
          ledgerTotalAmount: TOTAL_CLIENT_IMPACT_EUROS,
          difference: 0,
          transactionCount: 1,
          ledgerEntryCount: 3 // 2 client debits + 1 omnibus credit
        })
      );
      expect(orderResult!.discrepancies).toBeUndefined();
    });

    it("should validate reward order settlement without fees", async () => {
      const REWARD_AMOUNT_CENTS = 250_000; // €2,500.00 in cents
      const REWARD_AMOUNT_EUROS = Decimal.div(REWARD_AMOUNT_CENTS, 100).toNumber();

      // Create European user (EU/EEA client)
      const user = await buildUser({
        residencyCountry: "DE",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Create reward with settled order and no fees
      const reward = await buildReward({
        targetUser: user.id,
        consideration: { amount: REWARD_AMOUNT_CENTS, currency: "EUR" },
        fees: {
          fx: { amount: 0, currency: "EUR" },
          commission: { amount: 0, currency: "EUR" },
          executionSpread: { amount: 0, currency: "EUR" }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-order-2",
              status: "Matched",
              submittedAt: new Date(TODAY)
            }
          }
        },
        status: "Settled",
        updatedAt: new Date(TODAY)
      });

      // Create matching ledger entries for order settlement
      const description = `${user.id}|${reward.id}|${AccountingEventType.BONUS}`;

      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        // Client account debit (net settlement only)
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENT_EU_EEA,
          side: "debit",
          amount: REWARD_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: "INV-002",
          document_id: reward.id,
          owner_id: user.id
        },
        // Omnibus credit
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: REWARD_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: "INV-002",
          document_id: reward.id,
          owner_id: user.id
        }
      ]);

      const results = await AccountingValidationService.validateRewardsDbWithLedger(FROMDATE);

      const orderResult = results.find((r) => r.transactionType === "rewards_order");
      expect(orderResult).toEqual(
        expect.objectContaining({
          transactionType: "rewards_order",
          isValid: true,
          dbTotalAmount: REWARD_AMOUNT_EUROS,
          ledgerTotalAmount: REWARD_AMOUNT_EUROS,
          difference: 0,
          transactionCount: 1,
          ledgerEntryCount: 2 // 1 client debit + 1 omnibus credit
        })
      );
      expect(orderResult!.discrepancies).toBeUndefined();
    });

    it("should detect discrepancies in reward order settlement", async () => {
      const REWARD_AMOUNT_CENTS = 600_000; // €6,000.00 in cents
      const FX_FEE_AMOUNT_EUROS = 30; // €30.00 FX fee
      const FX_FEE_AMOUNT_CENTS = Decimal.mul(FX_FEE_AMOUNT_EUROS, 100).toNumber();
      const TOTAL_DB_AMOUNT_EUROS = Decimal.div(REWARD_AMOUNT_CENTS + FX_FEE_AMOUNT_CENTS, 100).toNumber();
      const LEDGER_AMOUNT_EUROS = 5000; // Different amount in ledger

      // Create European user (international client)
      const user = await buildUser({
        residencyCountry: "US",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Create reward with settled order
      const reward = await buildReward({
        targetUser: user.id,
        consideration: { amount: REWARD_AMOUNT_CENTS, currency: "EUR" },
        fees: {
          fx: { amount: FX_FEE_AMOUNT_EUROS, currency: "EUR" },
          commission: { amount: 0, currency: "EUR" },
          executionSpread: { amount: 0, currency: "EUR" }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-order-3",
              status: "Matched",
              submittedAt: new Date(TODAY)
            }
          }
        },
        status: "Settled",
        updatedAt: new Date(TODAY)
      });

      // Create mismatched ledger entries
      const description = `${user.id}|${reward.id}|${AccountingEventType.BONUS}`;

      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        // Client account debit (wrong amount)
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENT_INTERNATIONAL,
          side: "debit",
          amount: LEDGER_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: "INV-003",
          document_id: reward.id,
          owner_id: user.id
        },
        // Omnibus credit (matching the wrong client debit)
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: LEDGER_AMOUNT_EUROS,
          description,
          article_date: TODAY,
          reference_number: "INV-003",
          document_id: reward.id,
          owner_id: user.id
        }
      ]);

      const results = await AccountingValidationService.validateRewardsDbWithLedger(FROMDATE);

      const orderResult = results.find((r) => r.transactionType === "rewards_order");
      expect(orderResult).toEqual(
        expect.objectContaining({
          isValid: false,
          dbTotalAmount: TOTAL_DB_AMOUNT_EUROS,
          ledgerTotalAmount: LEDGER_AMOUNT_EUROS,
          difference: Decimal.sub(TOTAL_DB_AMOUNT_EUROS, LEDGER_AMOUNT_EUROS).toNumber(),
          transactionCount: 1,
          ledgerEntryCount: 2,
          discrepancies: expect.arrayContaining([
            expect.objectContaining({
              transactionId: reward.id,
              difference: Decimal.sub(TOTAL_DB_AMOUNT_EUROS, LEDGER_AMOUNT_EUROS).toNumber()
            })
          ])
        })
      );
    });
  });

  /* ------------------------------------------------------------------
   * Edge Cases and Error Handling
   * ------------------------------------------------------------------ */
  describe("Edge Cases", () => {
    it("should handle rewards with no matching ledger entries", async () => {
      // Create European user
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Create reward with settled deposit but no ledger entries
      await buildReward({
        targetUser: user.id,
        consideration: { amount: 100_000, currency: "EUR" },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-deposit-1",
              status: "Settled",
              submittedAt: new Date(TODAY)
            }
          }
        },
        updatedAt: new Date(TODAY)
      });

      const results = await AccountingValidationService.validateRewardsDbWithLedger(FROMDATE);

      const depositResult = results.find((r) => r.transactionType === "rewards_deposit");
      expect(depositResult).toBeDefined();
      expect(depositResult!.isValid).toBe(false);
      expect(depositResult!.dbTotalAmount).toBe(1000); // €1,000.00
      expect(depositResult!.ledgerTotalAmount).toBe(0);
      expect(depositResult!.difference).toBe(1000);
      expect(depositResult!.transactionCount).toBe(1);
      expect(depositResult!.ledgerEntryCount).toBe(0);
    });

    it("should filter rewards by date correctly", async () => {
      const OLD_DATE = "2024-12-01";

      // Create European user
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Create old reward (before fromDate)
      await buildReward({
        targetUser: user.id,
        consideration: { amount: 100_000, currency: "EUR" },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-deposit-old",
              status: "Settled",
              submittedAt: new Date(OLD_DATE)
            }
          }
        },
        updatedAt: new Date(OLD_DATE)
      });

      // Create new reward (after fromDate)
      const newReward = await buildReward({
        targetUser: user.id,
        consideration: { amount: 200_000, currency: "EUR" },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-deposit-new",
              status: "Settled",
              submittedAt: new Date(TODAY)
            }
          }
        },
        updatedAt: new Date(TODAY)
      });

      // Create ledger entries for both rewards
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        // Old reward entries (should be ignored)
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: 1000,
          description: `${user.id}|old-reward-id|${AccountingEventType.BONUS}`,
          article_date: OLD_DATE,
          reference_number: undefined,
          document_id: "old-reward-id",
          owner_id: user.id
        },
        // New reward entries (should be included)
        {
          aa: 2,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: 2000,
          description: `${user.id}|${newReward.id}|${AccountingEventType.BONUS}`,
          article_date: TODAY,
          reference_number: undefined,
          document_id: newReward.id,
          owner_id: user.id
        },
        {
          aa: 2,
          account_code: LedgerAccounts.BONUS_EXPENSE,
          side: "credit",
          amount: 2000,
          description: `${user.id}|${newReward.id}|${AccountingEventType.BONUS}`,
          article_date: TODAY,
          reference_number: undefined,
          document_id: newReward.id,
          owner_id: user.id
        }
      ]);

      const results = await AccountingValidationService.validateRewardsDbWithLedger(FROMDATE);

      const depositResult = results.find((r) => r.transactionType === "rewards_deposit");
      expect(depositResult).toBeDefined();
      expect(depositResult!.transactionCount).toBe(1); // Only new reward should be counted
      expect(depositResult!.dbTotalAmount).toBe(2000); // Only €2,000.00 from new reward
    });
  });

  /* ------------------------------------------------------------------
   * Integration with validateAllDbWithLedger
   * ------------------------------------------------------------------ */
  describe("Integration", () => {
    it("should include rewards validation in validateAllDbWithLedger", async () => {
      // Create European user
      const user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });

      // Create reward with settled deposit
      const reward = await buildReward({
        targetUser: user.id,
        consideration: { amount: 100_000, currency: "EUR" },
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: "wk-reward-deposit-1",
              status: "Settled",
              submittedAt: new Date(TODAY)
            }
          }
        },
        updatedAt: new Date(TODAY)
      });

      // Create matching ledger entries
      const description = `${user.id}|${reward.id}|${AccountingEventType.BONUS}`;
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "debit",
          amount: 1000,
          description,
          article_date: TODAY,
          reference_number: undefined,
          document_id: reward.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.BONUS_EXPENSE,
          side: "credit",
          amount: 1000,
          description,
          article_date: TODAY,
          reference_number: undefined,
          document_id: reward.id,
          owner_id: user.id
        }
      ]);

      const allResults = await AccountingValidationService.validateAllDbWithLedger(FROMDATE);

      // Should include rewards results alongside other validation results
      const rewardsResults = allResults.filter(
        (r) => r.transactionType === "rewards_deposit" || r.transactionType === "rewards_order"
      );
      expect(rewardsResults).toHaveLength(2);

      const depositResult = rewardsResults.find((r) => r.transactionType === "rewards_deposit");
      expect(depositResult!.isValid).toBe(true);
      expect(depositResult!.dbTotalAmount).toBe(1000);
    });
  });
});
